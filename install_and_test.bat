@echo off
echo ========================================
echo Android Trace Demo - Install and Test
echo ========================================
echo.

REM Check if adb is available
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: adb is not found in PATH. Please install Android SDK Platform Tools.
    pause
    exit /b 1
)

REM Check if device is connected
adb devices | findstr "device" >nul
if %errorlevel% neq 0 (
    echo Error: No Android device found. Please connect your device and enable USB debugging.
    pause
    exit /b 1
)

echo Device connected successfully!
echo.

REM Install the APK
echo Installing Trace Demo app...
adb install -r app\build\outputs\apk\debug\app-debug.apk

if %errorlevel% neq 0 (
    echo Error: Failed to install APK. Please check device permissions.
    pause
    exit /b 1
)

echo.
echo App installed successfully!
echo.

REM Launch the app
echo Launching the app...
adb shell am start -n com.example.trace/.MainActivity

echo.
echo ========================================
echo App launched! 
echo.
echo Next steps:
echo 1. The Trace Demo app should now be open on your device
echo 2. You can now run trace collection using:
echo    - run_trace.bat (for automated trace collection)
echo    - Or manually interact with the app buttons
echo.
echo App features:
echo - Run Java Trace Operations
echo - Run Computation Task  
echo - Run Native C++ Operations
echo - Run Full Benchmark Suite
echo.
echo Each button will trigger different traced operations
echo that you can analyze with Perfetto.
echo ========================================

pause
