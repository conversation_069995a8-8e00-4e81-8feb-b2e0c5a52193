@echo off
echo ========================================
echo 快速Trace测试 - 配合Perfetto网页使用
echo ========================================
echo.

REM 检查设备连接
adb devices | findstr "device" >nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Android设备，请连接设备并启用USB调试
    pause
    exit /b 1
)

echo 设备已连接！
echo.

REM 确保应用正在运行
echo 启动Trace Demo应用...
adb shell am start -n com.example.trace/.MainActivity
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo 准备就绪！
echo ========================================
echo.
echo 现在请按以下步骤操作：
echo.
echo 1. 打开浏览器访问: https://ui.perfetto.dev/
echo 2. 点击 "Record new trace"
echo 3. 选择你的Android设备
echo 4. 在CPU部分选择 "Coarse CPU usage counter"
echo 5. 设置Poll interval为1000ms
echo 6. 设置Duration为10秒
echo 7. 点击 "Start Recording"
echo.
echo 8. 录制开始后，立即按任意键继续...
pause

echo.
echo ========================================
echo 触发Trace操作 (请在10秒内完成)
echo ========================================
echo.

echo 第1步: 触发Java Trace操作...
adb shell input tap 540 400
timeout /t 2 /nobreak >nul

echo 第2步: 触发计算任务...
adb shell input tap 540 500
timeout /t 2 /nobreak >nul

echo 第3步: 触发Native C++操作...
adb shell input tap 540 600
timeout /t 2 /nobreak >nul

echo 第4步: 触发完整基准测试...
adb shell input tap 540 700
timeout /t 4 /nobreak >nul

echo.
echo ========================================
echo 操作完成！
echo ========================================
echo.
echo 所有trace操作已触发！
echo.
echo 现在你应该能在Perfetto UI中看到：
echo - CPU使用率峰值
echo - com.example.trace进程活动
echo - 系统统计数据
echo - 进程生命周期事件
echo.
echo 分析提示：
echo 1. 查看CPU图表中的使用率峰值
echo 2. 在进程列表中找到com.example.trace
echo 3. 观察1000ms间隔的采样点
echo 4. 检查进程创建和退出事件
echo.

pause
