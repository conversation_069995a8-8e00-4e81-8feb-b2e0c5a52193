# Android Perfetto Trace项目使用说明

## 项目完成状态 ✅

你的Android Trace项目已经完全设置完成！包含：

### ✅ 已完成的功能
- **Java trace标签** - TraceHelper.java 和 ComputationTask.java
- **C++ trace标签** - trace_utils.cpp 和 native-lib.cpp  
- **APK编译成功** - app-debug.apk已生成
- **应用安装成功** - 已安装到你的Android设备
- **应用运行正常** - MainActivity已启动

### ✅ 可追踪的函数
**Java函数:**
- `TraceHelper.executeComputationWithTrace`
- `TraceHelper.performHeavyComputation`
- `TraceHelper.performDataProcessing`
- `ComputationTask.performMathematicalComputations`
- `ComputationTask.performDataSortingOperations`
- `MainActivity.onCreate`

**C++函数:**
- `TraceUtils::performHeavyComputation`
- `TraceUtils::performSortingOperations`
- `TraceUtils::performMemoryOperations`
- `MainActivity.stringFromJNI`
- `MainActivity.performNativeComputation`

## 🚀 现在开始使用Perfetto网页抓取

### 第1步: 打开Perfetto UI
访问: **https://ui.perfetto.dev/**

### 第2步: 配置录制
1. 点击 "Record new trace"
2. 选择你的Android设备
3. 在 **CPU** 部分:
   - ✅ 选择 "Coarse CPU usage counter"
   - ✅ 设置 "Poll interval" 为 **1000ms**
4. 设置 "Duration" 为 **10秒**

### 第3步: 开始录制并操作应用
1. 点击 "Start Recording"
2. **立即**运行测试脚本:
   ```bash
   quick_test.bat
   ```
   或手动在设备上点击应用按钮

### 第4步: 应用操作序列（10秒内）
在你的Android设备上按顺序点击：
1. **"Run Java Trace Operations"** (0-2秒)
2. **"Run Computation Task"** (2-4秒)  
3. **"Run Native C++ Operations"** (4-6秒)
4. **"Run Full Benchmark Suite"** (6-10秒)

## 📊 预期的Trace结果

在Perfetto UI中你应该看到：

### CPU使用率图表
- 📈 应用运行时的CPU使用率峰值
- 🔄 1000ms间隔的采样点
- ⚡ 计算密集型操作的CPU峰值

### 进程活动
- 📱 "com.example.trace" 进程
- 🧵 主线程和工作线程活动
- ⏱️ 进程生命周期事件

### 系统统计
- 💻 CPU时间分布
- 🔀 进程创建/退出事件
- 📊 系统资源使用情况

## 🛠️ 可用的脚本工具

### 快速测试
```bash
quick_test.bat          # 配合网页界面的快速测试
```

### 命令行方式（备选）
```bash
run_trace.bat           # 完全自动化的trace收集
test_trace_complete.bat # 完整的自动化测试
```

### 应用管理
```bash
install_and_test.bat    # 安装应用并启动
```

## 🔍 分析技巧

### 在Perfetto UI中查找：
1. **CPU图表** - 寻找使用率峰值
2. **进程视图** - 找到com.example.trace
3. **时间线** - 观察操作的时序关系
4. **统计数据** - 查看资源消耗模式

### 关键指标：
- CPU使用率峰值时间
- 操作执行持续时间
- 线程切换频率
- 内存分配模式

## ⚡ 立即开始

你现在就可以开始抓取trace了！

1. 打开 https://ui.perfetto.dev/
2. 按照上述配置设置
3. 运行 `quick_test.bat` 或手动操作应用
4. 分析结果

## 📞 如果遇到问题

### 常见问题：
- **设备未显示**: 检查USB调试是否启用
- **应用无响应**: 重新运行 `adb shell am start -n com.example.trace/.MainActivity`
- **没有CPU数据**: 确保设备权限正确

### 调试命令：
```bash
adb devices                    # 检查设备连接
adb logcat -s TraceHelper     # 查看应用日志
adb shell ps | grep trace     # 检查应用进程
```

---

**🎉 恭喜！你的Android Perfetto Trace项目已经完全准备就绪！**

现在就去 https://ui.perfetto.dev/ 开始你的第一次trace抓取吧！
