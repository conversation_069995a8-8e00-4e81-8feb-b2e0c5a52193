# Android Perfetto Trace Demo

这个项目演示了如何在Android应用中使用Perfetto进行性能追踪，包含Java和C++代码的trace标签。

## 项目结构

### Java文件
- **MainActivity.java** - 主活动，包含UI和trace调用
- **TraceHelper.java** - Java trace工具类，包含各种可追踪的操作
- **ComputationTask.java** - 计算任务类，演示复杂的trace场景

### C++文件
- **native-lib.cpp** - JNI接口，连接Java和C++
- **trace_utils.cpp** - C++工具函数，包含各种可追踪的操作
- **trace_utils.h** - C++头文件

### 配置文件
- **config.pbtx** - Perfetto配置文件
- **run_trace.bat** - Windows批处理脚本，用于运行trace
- **run_trace.sh** - Linux/Mac shell脚本，用于运行trace

## 功能特性

### Java Trace操作
- 数学计算（质数、斐波那契数列、矩阵运算）
- 数据处理（字符串操作、内存分配）
- 网络模拟（延迟模拟）
- 嵌套trace操作
- 并行操作模拟

### C++ Trace操作
- 重计算（数学运算、质数计算、矩阵操作）
- 排序算法（随机数据生成、排序、二分搜索）
- 内存操作（大数组分配、字符串操作）
- 并发操作模拟
- 完整基准测试套件

## 使用方法

### 1. 编译和安装应用

```bash
# 使用Android Studio打开项目
# 或者使用命令行编译
./gradlew assembleDebug
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 2. 准备设备

确保你的Android设备：
- 已连接到电脑
- 启用了USB调试
- 已安装Perfetto（Android 9+自带）

### 3. 运行Trace收集

#### Windows:
```cmd
run_trace.bat
```

#### Linux/Mac:
```bash
chmod +x run_trace.sh
./run_trace.sh
```

#### 手动运行:
```bash
# 推送配置文件
adb push config.pbtx /data/local/tmp/config.pbtx

# 开始trace收集
cat config.pbtx | adb shell perfetto -c - --txt -o /data/misc/perfetto-traces/trace.pftrace

# 在另一个终端中操作应用，然后停止trace

# 拉取trace文件
adb pull /data/misc/perfetto-traces/trace.pftrace ./trace.pftrace
```

### 4. 操作应用

在trace收集期间：
1. 打开Trace应用
2. 点击"Run Java Trace Operations"按钮
3. 点击"Run Computation Task"按钮
4. 点击"Run Native C++ Operations"按钮
5. 点击"Run Full Benchmark Suite"按钮
6. 等待操作完成

### 5. 分析Trace结果

1. 访问 [Perfetto UI](https://ui.perfetto.dev/)
2. 上传生成的 `trace.pftrace` 文件
3. 查找以下trace标签：

#### Java Trace标签:
- `MainActivity.*` - 主活动方法
- `TraceHelper.*` - Java工具类方法
- `ComputationTask.*` - 计算任务方法

#### C++ Trace标签:
- `TraceUtils::*` - C++工具函数
- `MainActivity.*` - JNI调用

## Perfetto配置说明

当前配置包含：
- **CPU使用率统计** - 1000ms间隔
- **进程统计** - 启动时扫描所有进程
- **系统统计** - CPU时间和fork计数
- **Ftrace事件** - 进程生命周期事件

配置文件 `config.pbtx` 可以根据需要修改以收集不同类型的数据。

## 预期的Trace结果

在Perfetto UI中，你应该能看到：

1. **CPU使用率图表** - 显示应用运行时的CPU使用情况
2. **进程线程视图** - 显示应用的主线程和工作线程
3. **Trace事件** - 显示所有带标签的函数调用
4. **时间线** - 显示各个操作的执行时间和重叠情况

### 关键指标
- 函数执行时间
- CPU使用率峰值
- 内存分配模式
- 线程切换频率

## 故障排除

### 常见问题

1. **adb未找到**
   - 安装Android SDK Platform Tools
   - 将adb添加到系统PATH

2. **设备未连接**
   - 检查USB连接
   - 启用USB调试
   - 运行 `adb devices` 确认设备可见

3. **权限错误**
   - 确保设备已root或使用开发者选项
   - 某些设备可能需要特殊权限

4. **Trace文件为空**
   - 确保在trace收集期间操作了应用
   - 检查设备存储空间
   - 验证Perfetto配置

### 调试技巧

1. 使用 `adb logcat` 查看应用日志
2. 检查trace标签是否正确显示在日志中
3. 验证native库是否正确加载
4. 确保所有按钮点击都有响应

## 扩展功能

你可以通过以下方式扩展这个项目：

1. **添加更多trace点** - 在关键代码路径添加trace标签
2. **自定义配置** - 修改 `config.pbtx` 收集不同数据
3. **性能基准** - 添加更多计算密集型操作
4. **内存分析** - 添加内存分配trace
5. **网络追踪** - 添加网络请求trace

## 参考资料

- [Perfetto官方文档](https://perfetto.dev/docs/)
- [Android Trace API](https://developer.android.com/reference/android/os/Trace)
- [Android NDK Tracing](https://developer.android.com/ndk/reference/group/tracing)
