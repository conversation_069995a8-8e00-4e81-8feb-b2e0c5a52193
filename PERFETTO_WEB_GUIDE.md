# Perfetto网页界面Trace抓取指南

## 概述
本指南将帮助你使用Perfetto网页界面抓取Android应用的trace数据，并分析Java和C++函数的性能表现。

## 准备工作

### 1. 确保应用已安装并运行
```bash
# 检查设备连接
adb devices

# 安装应用（如果还没安装）
adb install -r app\build\outputs\apk\debug\app-debug.apk

# 启动应用
adb shell am start -n com.example.trace/.MainActivity
```

### 2. 验证配置文件
确保你的 `config.pbtx` 文件内容正确：
- 包含CPU统计（1000ms间隔）
- 包含进程统计
- 包含系统统计
- 持续时间10秒

## 使用Perfetto网页界面抓取Trace

### 方法1: 使用网页界面配置（推荐）

1. **打开Perfetto UI**
   - 访问: https://ui.perfetto.dev/
   - 点击左侧的 "Record new trace"

2. **配置Target device**
   - 选择 "Android device"
   - 确保你的设备出现在列表中

3. **配置Probes**
   - 在 "CPU" 部分选择 "Coarse CPU usage counter"
   - 设置 "Poll interval" 为 1000ms
   - 可以额外选择其他需要的probes

4. **设置Recording settings**
   - Duration: 10 seconds（或根据需要调整）
   - Buffer size: 可以保持默认

5. **开始录制**
   - 点击 "Start Recording"
   - 立即切换到你的Android设备

### 方法2: 使用命令行配置

如果你更喜欢使用现有的配置文件：

```bash
# 使用你提供的配置
cat config.pbtx | adb shell perfetto -c - --txt -o /data/misc/perfetto-traces/trace.pftrace
```

## 操作应用触发Trace事件

在trace录制开始后，立即在Android设备上操作应用：

### 建议的操作序列（10秒内完成）：

1. **0-2秒**: 点击 "Run Java Trace Operations"
   - 这会触发TraceHelper中的各种Java trace标签

2. **2-4秒**: 点击 "Run Computation Task"
   - 这会触发ComputationTask中的数学计算trace

3. **4-6秒**: 点击 "Run Native C++ Operations"
   - 这会触发C++中的native trace标签

4. **6-8秒**: 点击 "Run Full Benchmark Suite"
   - 这会触发完整的基准测试

5. **8-10秒**: 等待所有操作完成

## 分析Trace结果

### 在Perfetto UI中查找的关键信息：

#### 1. CPU使用率
- 查看CPU使用率图表
- 寻找应用运行时的CPU峰值
- 观察1000ms间隔的采样点

#### 2. 进程活动
- 在进程列表中找到 "com.example.trace"
- 查看主线程和工作线程的活动

#### 3. 系统统计
- CPU时间分布
- Fork计数变化
- 进程生命周期事件

#### 4. 预期的Trace标签（如果启用了atrace）
虽然当前配置主要关注CPU统计，但如果你的设备支持，可能会看到：

**Java Trace标签:**
- `MainActivity.onCreate`
- `MainActivity.startTraceOperations`
- `TraceHelper.executeComputationWithTrace`
- `TraceHelper.performHeavyComputation`
- `ComputationTask.executeComputationSuite`

**C++ Trace标签:**
- `MainActivity.stringFromJNI`
- `MainActivity.performNativeComputation`
- `TraceUtils::performHeavyComputation`

## 故障排除

### 如果看不到应用的trace标签：

1. **启用atrace支持**
   在config.pbtx中添加：
   ```
   data_sources {
     config {
       name: "android.atrace"
       atrace_config {
         categories: "app"
         categories: "dalvik"
       }
     }
   }
   ```

2. **检查应用权限**
   ```bash
   # 确保应用有trace权限
   adb shell setprop debug.atrace.tags.enableflags 0
   adb shell setprop debug.atrace.app_number 10000
   ```

3. **验证trace标签**
   ```bash
   # 检查应用日志
   adb logcat -s TraceHelper ComputationTask MainActivity
   ```

### 如果CPU数据不显示：

1. **检查设备权限**
   - 确保设备已启用开发者选项
   - 确保USB调试已启用

2. **验证Perfetto版本**
   ```bash
   adb shell perfetto --version
   ```

## 优化建议

### 为了获得更好的trace数据：

1. **增加采样频率**
   - 将poll interval从1000ms减少到500ms或100ms

2. **增加缓冲区大小**
   - 如果数据丢失，增加buffer size_kb

3. **延长录制时间**
   - 将duration_ms增加到20000或30000

4. **添加更多数据源**
   ```
   data_sources {
     config {
       name: "android.atrace"
       atrace_config {
         categories: "app"
         categories: "sched"
         categories: "freq"
       }
     }
   }
   ```

## 预期结果

使用你的配置，你应该能看到：

1. **CPU使用率图表** - 显示应用运行时的CPU消耗
2. **进程统计** - 显示com.example.trace进程的活动
3. **系统事件** - 进程创建、退出等事件
4. **时间线** - 10秒的完整时间线视图

这些数据足以分析应用的基本性能特征和CPU使用模式。
