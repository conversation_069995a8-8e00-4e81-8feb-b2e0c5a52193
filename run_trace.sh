#!/bin/bash

echo "Starting Perfetto trace collection..."
echo ""
echo "Make sure your Android device is connected and USB debugging is enabled."
echo ""

# Check if adb is available
if ! command -v adb &> /dev/null; then
    echo "Error: adb is not found in PATH. Please install Android SDK Platform Tools."
    exit 1
fi

# Check if device is connected
if ! adb devices | grep -q "device"; then
    echo "Error: No Android device found. Please connect your device and enable USB debugging."
    exit 1
fi

echo "Device connected. Starting trace collection..."
echo ""

# Push config and start trace
echo "Pushing trace configuration..."
adb push config.pbtx /data/local/tmp/config.pbtx

echo ""
echo "Starting Perfetto trace collection..."
echo "This will run for 30 seconds. During this time, please:"
echo "1. Open the Trace app on your device"
echo "2. Click the various buttons to trigger traced operations"
echo "3. Wait for this script to complete"
echo ""

# Start trace collection (30 seconds)
echo "Collecting trace data for 30 seconds..."
timeout 30s bash -c 'cat config.pbtx | adb shell perfetto -c - --txt -o /data/misc/perfetto-traces/trace.pftrace' &

# Wait for the trace to complete
wait

echo ""
echo "Trace collection completed!"
echo ""

# Pull the trace file
echo "Pulling trace file from device..."
adb pull /data/misc/perfetto-traces/trace.pftrace ./trace.pftrace

if [ -f "trace.pftrace" ]; then
    echo ""
    echo "Success! Trace file saved as: trace.pftrace"
    echo ""
    echo "You can now analyze this trace file using:"
    echo "1. Perfetto UI: https://ui.perfetto.dev/"
    echo "2. Upload the trace.pftrace file to the web interface"
    echo ""
    echo "Look for the following trace sections in your analysis:"
    echo "- MainActivity.* (Java activity methods)"
    echo "- TraceHelper.* (Java trace helper methods)"
    echo "- ComputationTask.* (Java computation methods)"
    echo "- TraceUtils::* (C++ utility methods)"
    echo ""
else
    echo "Error: Failed to pull trace file from device."
    echo "Please check device permissions and try again."
fi

echo "Press any key to continue..."
read -n 1
