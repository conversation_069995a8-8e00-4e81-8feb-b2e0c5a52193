@echo off
echo ========================================
echo Complete Trace Test - Automated Demo
echo ========================================
echo.

REM Check if adb is available
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: adb is not found in PATH. Please install Android SDK Platform Tools.
    pause
    exit /b 1
)

REM Check if device is connected
adb devices | findstr "device" >nul
if %errorlevel% neq 0 (
    echo Error: No Android device found. Please connect your device and enable USB debugging.
    pause
    exit /b 1
)

echo Device connected successfully!
echo.

REM Make sure app is installed and running
echo Ensuring app is running...
adb shell am start -n com.example.trace/.MainActivity
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo Starting Perfetto Trace Collection
echo ========================================
echo.

REM Push config file
echo Pushing trace configuration...
adb push config.pbtx /data/local/tmp/config.pbtx

echo.
echo Starting trace collection in background...
echo This will collect trace data for 45 seconds.
echo.

REM Start trace collection in background
start /B cmd /c "cat config.pbtx | adb shell perfetto -c - --txt -o /data/misc/perfetto-traces/trace.pftrace"

REM Wait a moment for trace to start
timeout /t 3 /nobreak >nul

echo Trace collection started!
echo.
echo ========================================
echo Triggering Traced Operations
echo ========================================
echo.

REM Simulate button clicks to trigger traced operations
echo Triggering Java trace operations...
adb shell input tap 540 400
timeout /t 3 /nobreak >nul

echo Triggering computation task...
adb shell input tap 540 500
timeout /t 5 /nobreak >nul

echo Triggering native C++ operations...
adb shell input tap 540 600
timeout /t 5 /nobreak >nul

echo Triggering benchmark suite...
adb shell input tap 540 700
timeout /t 8 /nobreak >nul

echo.
echo All operations triggered! Waiting for trace collection to complete...
echo.

REM Wait for remaining trace time
timeout /t 20 /nobreak

echo.
echo ========================================
echo Collecting Trace Results
echo ========================================
echo.

REM Pull the trace file
echo Pulling trace file from device...
adb pull /data/misc/perfetto-traces/trace.pftrace ./trace_complete.pftrace

if exist trace_complete.pftrace (
    echo.
    echo ========================================
    echo SUCCESS! Trace Collection Complete
    echo ========================================
    echo.
    echo Trace file saved as: trace_complete.pftrace
    echo File size:
    dir trace_complete.pftrace | findstr trace_complete.pftrace
    echo.
    echo ========================================
    echo Analysis Instructions
    echo ========================================
    echo.
    echo 1. Open Perfetto UI: https://ui.perfetto.dev/
    echo 2. Upload the trace_complete.pftrace file
    echo 3. Look for these trace sections:
    echo.
    echo    Java Traces:
    echo    - MainActivity.onCreate
    echo    - MainActivity.startTraceOperations
    echo    - TraceHelper.executeComputationWithTrace
    echo    - TraceHelper.performHeavyComputation
    echo    - ComputationTask.executeComputationSuite
    echo    - ComputationTask.performMathematicalComputations
    echo.
    echo    C++ Traces:
    echo    - MainActivity.stringFromJNI
    echo    - MainActivity.performNativeComputation
    echo    - TraceUtils::performHeavyComputation
    echo    - TraceUtils::performSortingOperations
    echo    - TraceUtils::performMemoryOperations
    echo.
    echo ========================================
    echo Expected Results
    echo ========================================
    echo.
    echo In the Perfetto UI, you should see:
    echo - CPU usage spikes during computation
    echo - Clear function call hierarchies
    echo - Timing information for each operation
    echo - Thread activity patterns
    echo - Memory allocation patterns
    echo.
    echo The trace should show the complete execution
    echo flow of both Java and C++ operations with
    echo precise timing and performance metrics.
    echo.
) else (
    echo.
    echo ========================================
    echo ERROR: Trace Collection Failed
    echo ========================================
    echo.
    echo Failed to pull trace file from device.
    echo.
    echo Troubleshooting steps:
    echo 1. Check device permissions
    echo 2. Ensure device has sufficient storage
    echo 3. Verify Perfetto is available on device
    echo 4. Try running trace collection manually
    echo.
    echo Manual trace command:
    echo cat config.pbtx ^| adb shell perfetto -c - --txt -o /data/misc/perfetto-traces/trace.pftrace
    echo.
)

echo.
echo ========================================
echo Test Complete
echo ========================================
pause
