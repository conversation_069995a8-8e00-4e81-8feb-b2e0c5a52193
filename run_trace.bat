@echo off
echo Starting Perfetto trace collection...
echo.
echo Make sure your Android device is connected and USB debugging is enabled.
echo.

REM Check if adb is available
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: adb is not found in PATH. Please install Android SDK Platform Tools.
    pause
    exit /b 1
)

REM Check if device is connected
adb devices | findstr "device" >nul
if %errorlevel% neq 0 (
    echo Error: No Android device found. Please connect your device and enable USB debugging.
    pause
    exit /b 1
)

echo Device connected. Starting trace collection...
echo.

REM Push config and start trace
echo Pushing trace configuration...
adb push config.pbtx /data/local/tmp/config.pbtx

echo.
echo Starting Perfetto trace collection...
echo This will run for 30 seconds. During this time, please:
echo 1. Open the Trace app on your device
echo 2. Click the various buttons to trigger traced operations
echo 3. Wait for this script to complete
echo.

REM Start trace collection (30 seconds)
adb shell "cat /data/local/tmp/config.pbtx | perfetto -c - --txt -o /data/misc/perfetto-traces/trace.pftrace" &

REM Wait for 30 seconds
echo Collecting trace data for 30 seconds...
timeout /t 30 /nobreak

echo.
echo Trace collection completed!
echo.

REM Pull the trace file
echo Pulling trace file from device...
adb pull /data/misc/perfetto-traces/trace.pftrace ./trace.pftrace

if exist trace.pftrace (
    echo.
    echo Success! Trace file saved as: trace.pftrace
    echo.
    echo You can now analyze this trace file using:
    echo 1. Perfetto UI: https://ui.perfetto.dev/
    echo 2. Upload the trace.pftrace file to the web interface
    echo.
    echo Look for the following trace sections in your analysis:
    echo - MainActivity.* (Java activity methods)
    echo - TraceHelper.* (Java trace helper methods)
    echo - ComputationTask.* (Java computation methods)
    echo - TraceUtils::* (C++ utility methods)
    echo.
) else (
    echo Error: Failed to pull trace file from device.
    echo Please check device permissions and try again.
)

pause
