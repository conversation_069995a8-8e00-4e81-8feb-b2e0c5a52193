# Perfetto配置文件 - 专为网页界面优化
# 适用于Android应用trace分析

buffers {
  size_kb: 32768
  fill_policy: DISCARD
}

buffers {
  size_kb: 8192
  fill_policy: DISCARD
}

# Android应用trace数据源
data_sources {
  config {
    name: "android.packages_list"
  }
}

# 系统调用trace
data_sources {
  config {
    name: "linux.ftrace"
    ftrace_config {
      # 进程相关事件
      ftrace_events: "sched/sched_switch"
      ftrace_events: "sched/sched_process_exit"
      ftrace_events: "sched/sched_process_free"
      ftrace_events: "task/task_newtask"
      ftrace_events: "task/task_rename"
      
      # CPU相关事件
      ftrace_events: "power/cpu_frequency"
      ftrace_events: "power/cpu_idle"
      
      # 内存相关事件
      ftrace_events: "kmem/rss_stat"
      ftrace_events: "kmem/ion_heap_grow"
      ftrace_events: "kmem/ion_heap_shrink"
      
      # 应用trace标记
      ftrace_events: "ftrace/print"
    }
  }
}

# 进程统计
data_sources {
  config {
    name: "linux.process_stats"
    process_stats_config {
      scan_all_processes_on_start: true
      proc_stats_poll_ms: 1000
    }
  }
}

# 系统统计
data_sources {
  config {
    name: "linux.sys_stats"
    sys_stats_config {
      stat_period_ms: 1000
      stat_counters: STAT_CPU_TIMES
      stat_counters: STAT_FORK_COUNT
      stat_counters: STAT_MEMINFO
    }
  }
}

# Android atrace数据源 - 这是关键！
data_sources {
  config {
    name: "android.atrace"
    atrace_config {
      categories: "app"
      categories: "dalvik"
      categories: "java"
      categories: "bionic"
      categories: "sched"
      categories: "freq"
      categories: "idle"
      categories: "load"
      categories: "memory"
    }
  }
}

# 持续时间设置
duration_ms: 30000
