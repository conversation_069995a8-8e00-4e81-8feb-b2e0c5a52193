package com.example.trace;

import android.os.AsyncTask;
import android.os.Trace;
import android.util.Log;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 * Computation task class with trace markers for performance analysis
 * This class demonstrates various computational operations that can be traced
 */
public class ComputationTask {
    private static final String TAG = "ComputationTask";
    private static final Random random = new Random();

    /**
     * Interface for computation task callbacks
     */
    public interface ComputationCallback {
        void onTaskStarted();
        void onTaskProgress(int progress);
        void onTaskCompleted(String result);
        void onTaskError(String error);
    }

    /**
     * Execute a series of computational tasks with trace markers
     */
    public static void executeComputationSuite(ComputationCallback callback) {
        Trace.beginSection("ComputationTask.executeComputationSuite");
        try {
            if (callback != null) {
                callback.onTaskStarted();
            }

            // Task 1: Mathematical computations
            String mathResult = performMathematicalComputations();
            if (callback != null) {
                callback.onTaskProgress(25);
            }

            // Task 2: Data sorting and searching
            String sortResult = performDataSortingOperations();
            if (callback != null) {
                callback.onTaskProgress(50);
            }

            // Task 3: String processing
            String stringResult = performStringProcessing();
            if (callback != null) {
                callback.onTaskProgress(75);
            }

            // Task 4: Memory operations
            String memoryResult = performMemoryOperations();
            if (callback != null) {
                callback.onTaskProgress(100);
            }

            String finalResult = String.format(
                "Computation Suite Results:\n%s\n%s\n%s\n%s",
                mathResult, sortResult, stringResult, memoryResult
            );

            if (callback != null) {
                callback.onTaskCompleted(finalResult);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in computation suite", e);
            if (callback != null) {
                callback.onTaskError("Computation failed: " + e.getMessage());
            }
        } finally {
            Trace.endSection();
        }
    }

    /**
     * Perform mathematical computations with trace markers
     */
    private static String performMathematicalComputations() {
        Trace.beginSection("ComputationTask.performMathematicalComputations");
        try {
            Log.d(TAG, "Starting mathematical computations");

            // Prime number calculation
            Trace.beginSection("ComputationTask.calculatePrimes");
            List<Integer> primes = new ArrayList<>();
            try {
                for (int i = 2; i < 1000; i++) {
                    if (isPrime(i)) {
                        primes.add(i);
                    }
                }
            } finally {
                Trace.endSection();
            }

            // Fibonacci sequence
            Trace.beginSection("ComputationTask.calculateFibonacci");
            List<Long> fibonacci = new ArrayList<>();
            try {
                fibonacci.add(0L);
                fibonacci.add(1L);
                for (int i = 2; i < 30; i++) {
                    fibonacci.add(fibonacci.get(i-1) + fibonacci.get(i-2));
                }
            } finally {
                Trace.endSection();
            }

            // Matrix multiplication simulation
            Trace.beginSection("ComputationTask.matrixMultiplication");
            double result = 0;
            try {
                int size = 100;
                for (int i = 0; i < size; i++) {
                    for (int j = 0; j < size; j++) {
                        result += Math.sin(i) * Math.cos(j);
                    }
                }
            } finally {
                Trace.endSection();
            }

            return String.format("Math: Found %d primes, %d Fibonacci numbers, Matrix result: %.2f",
                    primes.size(), fibonacci.size(), result);

        } finally {
            Trace.endSection();
        }
    }

    /**
     * Check if a number is prime
     */
    private static boolean isPrime(int n) {
        if (n < 2) return false;
        for (int i = 2; i <= Math.sqrt(n); i++) {
            if (n % i == 0) return false;
        }
        return true;
    }

    /**
     * Perform data sorting and searching operations with trace markers
     */
    private static String performDataSortingOperations() {
        Trace.beginSection("ComputationTask.performDataSortingOperations");
        try {
            Log.d(TAG, "Starting data sorting operations");

            // Generate random data
            Trace.beginSection("ComputationTask.generateRandomData");
            List<Integer> data = new ArrayList<>();
            try {
                for (int i = 0; i < 10000; i++) {
                    data.add(random.nextInt(100000));
                }
            } finally {
                Trace.endSection();
            }

            // Sort the data
            Trace.beginSection("ComputationTask.sortData");
            List<Integer> sortedData = new ArrayList<>(data);
            try {
                Collections.sort(sortedData);
            } finally {
                Trace.endSection();
            }

            // Binary search operations
            Trace.beginSection("ComputationTask.binarySearch");
            int searchCount = 0;
            try {
                for (int i = 0; i < 100; i++) {
                    int target = random.nextInt(100000);
                    int index = Collections.binarySearch(sortedData, target);
                    if (index >= 0) {
                        searchCount++;
                    }
                }
            } finally {
                Trace.endSection();
            }

            return String.format("Sorting: Processed %d items, found %d search targets",
                    data.size(), searchCount);

        } finally {
            Trace.endSection();
        }
    }

    /**
     * Perform string processing operations with trace markers
     */
    private static String performStringProcessing() {
        Trace.beginSection("ComputationTask.performStringProcessing");
        try {
            Log.d(TAG, "Starting string processing");

            // String concatenation
            Trace.beginSection("ComputationTask.stringConcatenation");
            StringBuilder sb = new StringBuilder();
            try {
                for (int i = 0; i < 5000; i++) {
                    sb.append("String").append(i).append(" ");
                }
            } finally {
                Trace.endSection();
            }

            // String parsing and analysis
            Trace.beginSection("ComputationTask.stringAnalysis");
            String text = sb.toString();
            int wordCount = 0;
            int charCount = 0;
            try {
                String[] words = text.split("\\s+");
                wordCount = words.length;
                charCount = text.length();

                // Count specific patterns
                for (String word : words) {
                    if (word.contains("String")) {
                        // Pattern found
                    }
                }
            } finally {
                Trace.endSection();
            }

            return String.format("Strings: Processed %d characters, %d words",
                    charCount, wordCount);

        } finally {
            Trace.endSection();
        }
    }

    /**
     * Perform memory operations with trace markers
     */
    private static String performMemoryOperations() {
        Trace.beginSection("ComputationTask.performMemoryOperations");
        try {
            Log.d(TAG, "Starting memory operations");

            // Array operations
            Trace.beginSection("ComputationTask.arrayOperations");
            int arraySize = 50000;
            int[] intArray = new int[arraySize];
            try {
                for (int i = 0; i < arraySize; i++) {
                    intArray[i] = i * 2;
                }

                // Sum calculation
                long sum = 0;
                for (int value : intArray) {
                    sum += value;
                }
            } finally {
                Trace.endSection();
            }

            // List operations
            Trace.beginSection("ComputationTask.listOperations");
            List<String> stringList = new ArrayList<>();
            try {
                for (int i = 0; i < 10000; i++) {
                    stringList.add("Item_" + i);
                }

                // List manipulation
                Collections.shuffle(stringList);
                Collections.sort(stringList);
            } finally {
                Trace.endSection();
            }

            // Memory cleanup simulation
            Trace.beginSection("ComputationTask.memoryCleanup");
            try {
                // Force garbage collection hint
                System.gc();
                Thread.sleep(10); // Small delay to simulate cleanup
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                Trace.endSection();
            }

            return String.format("Memory: Processed %d array elements, %d list items",
                    arraySize, stringList.size());

        } finally {
            Trace.endSection();
        }
    }
}
