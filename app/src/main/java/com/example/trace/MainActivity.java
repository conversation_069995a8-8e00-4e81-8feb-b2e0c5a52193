package com.example.trace;

import androidx.appcompat.app.AppCompatActivity;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Trace;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.example.trace.databinding.ActivityMainBinding;

public class MainActivity extends AppCompatActivity implements ComputationTask.ComputationCallback {

    private static final String TAG = "MainActivity";

    // Used to load the 'trace' library on application startup.
    static {
        System.loadLibrary("trace");
    }

    private ActivityMainBinding binding;
    private TextView statusText;
    private Button startTraceButton;
    private Button startComputationButton;
    private Button startNativeButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Trace.beginSection("MainActivity.onCreate");
        try {
            super.onCreate(savedInstanceState);
            Log.d(TAG, "MainActivity onCreate started");

            binding = ActivityMainBinding.inflate(getLayoutInflater());
            setContentView(binding.getRoot());

            initializeViews();
            setupClickListeners();

            // Initial native call with trace
            updateStatusText("App initialized. Ready for tracing.");

            Log.d(TAG, "MainActivity onCreate completed");
        } finally {
            Trace.endSection();
        }
    }

    private void initializeViews() {
        Trace.beginSection("MainActivity.initializeViews");
        try {
            statusText = binding.sampleText;
            statusText.setText("Trace Demo App\nClick buttons to start traced operations");
        } finally {
            Trace.endSection();
        }
    }

    private void setupClickListeners() {
        Trace.beginSection("MainActivity.setupClickListeners");
        try {
            // We'll add buttons programmatically since we need to modify the layout
            // For now, set up click listener on the text view as a demo
            statusText.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startTraceOperations();
                }
            });
        } finally {
            Trace.endSection();
        }
    }

    private void startTraceOperations() {
        Trace.beginSection("MainActivity.startTraceOperations");
        try {
            updateStatusText("Starting trace operations...");

            // Run trace helper operations
            new Thread(new Runnable() {
                @Override
                public void run() {
                    TraceHelper.runTraceTest();
                    TraceHelper.executeComputationWithTrace();

                    // Update UI on main thread
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            updateStatusText("Trace operations completed!\nTap again to run more tests.");
                        }
                    });
                }
            }).start();
        } finally {
            Trace.endSection();
        }
    }

    private void startComputationTask() {
        Trace.beginSection("MainActivity.startComputationTask");
        try {
            updateStatusText("Starting computation task...");

            new Thread(new Runnable() {
                @Override
                public void run() {
                    ComputationTask.executeComputationSuite(MainActivity.this);
                }
            }).start();
        } finally {
            Trace.endSection();
        }
    }

    private void startNativeOperations() {
        Trace.beginSection("MainActivity.startNativeOperations");
        try {
            updateStatusText("Starting native operations...");

            new Thread(new Runnable() {
                @Override
                public void run() {
                    // Call native methods with tracing
                    String nativeResult = stringFromJNI();
                    String computationResult = performNativeComputation();
                    String sortingResult = performNativeSorting();

                    final String finalResult = String.format(
                        "Native Results:\n%s\n%s\n%s",
                        nativeResult, computationResult, sortingResult
                    );

                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            updateStatusText(finalResult);
                        }
                    });
                }
            }).start();
        } finally {
            Trace.endSection();
        }
    }

    private void updateStatusText(String text) {
        if (statusText != null) {
            statusText.setText(text);
        }
    }

    // ComputationTask.ComputationCallback implementation
    @Override
    public void onTaskStarted() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                updateStatusText("Computation task started...");
            }
        });
    }

    @Override
    public void onTaskProgress(int progress) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                updateStatusText("Computation progress: " + progress + "%");
            }
        });
    }

    @Override
    public void onTaskCompleted(String result) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                updateStatusText("Computation completed!\n" + result);
            }
        });
    }

    @Override
    public void onTaskError(String error) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                updateStatusText("Computation error: " + error);
                Toast.makeText(MainActivity.this, "Error: " + error, Toast.LENGTH_LONG).show();
            }
        });
    }

    /**
     * Native methods implemented in C++
     */
    public native String stringFromJNI();
    public native String performNativeComputation();
    public native String performNativeSorting();
}