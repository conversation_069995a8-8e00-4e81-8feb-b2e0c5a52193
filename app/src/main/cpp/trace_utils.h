#ifndef TRACE_UTILS_H
#define TRACE_UTILS_H

#include <string>

namespace TraceUtils {
    
    /**
     * Begin a trace section with the given name
     * @param name Name of the trace section
     */
    void beginTrace(const char* name);
    
    /**
     * End the current trace section
     */
    void endTrace();
    
    /**
     * Perform heavy computational operations with trace markers
     * @return String describing the computation results
     */
    std::string performHeavyComputation();
    
    /**
     * Perform sorting and searching operations with trace markers
     * @return String describing the sorting results
     */
    std::string performSortingOperations();
    
    /**
     * Perform memory-intensive operations with trace markers
     * @return String describing the memory operation results
     */
    std::string performMemoryOperations();
    
    /**
     * Perform concurrent operations simulation with trace markers
     * @return String describing the concurrent operation results
     */
    std::string performConcurrentOperations();
    
    /**
     * Run a complete benchmark suite with trace markers
     * @return String describing the benchmark results
     */
    std::string performBenchmarkSuite();
    
} // namespace TraceUtils

#endif // TRACE_UTILS_H
