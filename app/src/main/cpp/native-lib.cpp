#include <jni.h>
#include <string>
#include <android/trace.h>
#include <android/log.h>
#include "trace_utils.h"

#define LOG_TAG "NativeLib"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

extern "C" JNIEXPORT jstring JNICALL
Java_com_example_trace_MainActivity_stringFromJNI(
        JNIEnv* env,
        jobject /* this */) {

    ATrace_beginSection("MainActivity.stringFromJNI");
    LOGD("stringFromJ<PERSON> called");

    std::string hello = "Hello from C++ with Trace Support!";
    jstring result = env->NewStringUTF(hello.c_str());

    ATrace_endSection();
    return result;
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_example_trace_MainActivity_performNativeComputation(
        JNIEnv* env,
        jobject /* this */) {

    ATrace_beginSection("MainActivity.performNativeComputation");
    LOGI("Starting native computation");

    try {
        std::string result = TraceUtils::performHeavyComputation();
        jstring jResult = env->NewStringUTF(result.c_str());

        ATrace_endSection();
        return jResult;
    } catch (const std::exception& e) {
        LOGI("Error in native computation: %s", e.what());
        ATrace_endSection();
        return env->NewStringUTF("Error in native computation");
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_example_trace_MainActivity_performNativeSorting(
        JNIEnv* env,
        jobject /* this */) {

    ATrace_beginSection("MainActivity.performNativeSorting");
    LOGI("Starting native sorting");

    try {
        std::string result = TraceUtils::performSortingOperations();
        jstring jResult = env->NewStringUTF(result.c_str());

        ATrace_endSection();
        return jResult;
    } catch (const std::exception& e) {
        LOGI("Error in native sorting: %s", e.what());
        ATrace_endSection();
        return env->NewStringUTF("Error in native sorting");
    }
}

// Additional native methods for comprehensive testing
extern "C" JNIEXPORT jstring JNICALL
Java_com_example_trace_MainActivity_performNativeMemoryOps(
        JNIEnv* env,
        jobject /* this */) {

    ATrace_beginSection("MainActivity.performNativeMemoryOps");
    LOGI("Starting native memory operations");

    try {
        std::string result = TraceUtils::performMemoryOperations();
        jstring jResult = env->NewStringUTF(result.c_str());

        ATrace_endSection();
        return jResult;
    } catch (const std::exception& e) {
        LOGI("Error in native memory operations: %s", e.what());
        ATrace_endSection();
        return env->NewStringUTF("Error in native memory operations");
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_example_trace_MainActivity_performNativeBenchmark(
        JNIEnv* env,
        jobject /* this */) {

    ATrace_beginSection("MainActivity.performNativeBenchmark");
    LOGI("Starting native benchmark suite");

    try {
        std::string result = TraceUtils::performBenchmarkSuite();
        jstring jResult = env->NewStringUTF(result.c_str());

        ATrace_endSection();
        return jResult;
    } catch (const std::exception& e) {
        LOGI("Error in native benchmark: %s", e.what());
        ATrace_endSection();
        return env->NewStringUTF("Error in native benchmark");
    }
}