<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".MainActivity">

    <TextView
        android:id="@+id/title_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Perfetto Trace Demo"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/btn_java_trace"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Run Java Trace Operations"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintTop_toBottomOf="@id/title_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/btn_computation_task"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Run Computation Task"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintTop_toBottomOf="@id/btn_java_trace"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/btn_native_operations"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Run Native C++ Operations"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintTop_toBottomOf="@id/btn_computation_task"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/btn_benchmark_suite"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Run Full Benchmark Suite"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toBottomOf="@id/btn_native_operations"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/btn_benchmark_suite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/sample_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Trace Demo App\nClick buttons above to start traced operations.\n\nResults will appear here..."
            android:textSize="14sp"
            android:padding="8dp"
            android:background="@android:color/black"
            android:textColor="@android:color/white"
            android:fontFamily="monospace" />

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>